import { WebContainer } from '@webcontainer/api';
import { useFileStore } from '../../stores/fileStore';

let webcontainerInstance: WebContainer | null = null;
let bootPromise: Promise<WebContainer> | null = null;
let isInitialized = false;

// Cleanup function to properly dispose of WebContainer
export function cleanupWebContainer() {
  try {
    // WebContainer doesn't have a direct dispose method, but we can clean up references
    webcontainerInstance = null;
    bootPromise = null;
    isInitialized = false;
    console.log('WebContainer cleaned up');
  } catch (error) {
    console.warn('Error during WebContainer cleanup:', error);
  }
}

// Force reset WebContainer (useful for development)
export async function resetWebContainer() {
  console.log('Force resetting WebContainer...');
  cleanupWebContainer();
  // Also reset server state
  try {
    const { resetServerState } = await import('./server');
    resetServerState();
  } catch (error) {
    console.warn('Could not reset server state:', error);
  }
}

// Listen for page unload to cleanup
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', cleanupWebContainer);
  window.addEventListener('unload', cleanupWebContainer);
}

export async function getWebContainerInstance(): Promise<WebContainer | null> {
  // Return existing instance if available
  if (webcontainerInstance && isInitialized) {
    return webcontainerInstance;
  }

  // Return existing boot promise if in progress
  if (bootPromise) {
    return bootPromise;
  }

  try {
    console.log('Booting WebContainer...');
    bootPromise = WebContainer.boot();
    webcontainerInstance = await bootPromise;

    if (webcontainerInstance) {
      console.log('WebContainer booted successfully');

      // Initialize the root directory
      await webcontainerInstance.fs.mkdir('/', { recursive: true });

      // Mount initial files
      const { files } = useFileStore.getState();
      if (Object.keys(files).length > 0) {
        console.log('Mounting initial files...');
        for (const [path, contents] of Object.entries(files)) {
          try {
            const fullPath = `/${path}`;
            // Create parent directories
            const parentDir = fullPath.substring(0, fullPath.lastIndexOf('/'));
            if (parentDir && parentDir !== '/') {
              await webcontainerInstance.fs.mkdir(parentDir, { recursive: true });
            }
            // Write file
            await webcontainerInstance.fs.writeFile(fullPath, contents);
          } catch (fileError) {
            console.warn(`Failed to mount file ${path}:`, fileError);
          }
        }
      }

      isInitialized = true;
    }

    return webcontainerInstance;
  } catch (error) {
    console.error('Failed to boot WebContainer:', error);
    bootPromise = null;
    webcontainerInstance = null;
    isInitialized = false;
    return null;
  } finally {
    bootPromise = null;
  }
}