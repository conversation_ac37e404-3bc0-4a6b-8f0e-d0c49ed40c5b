import { WebContainer } from '@webcontainer/api';
import type { NodeContainer } from './nodecontainer/types';
import * as webContainer from './webcontainer';
import * as nodeContainer from './nodecontainer';

// Always use WebContainer in the web version (Electron support removed)
const isElectron = false;

export type Container = WebContainer | NodeContainer;

// Basic exports - always use webContainer
export const {
  useTerminalState,
  syncFileSystem,
  updateFileSystemNow,
  startDevServer,
} = webContainer;

// Container instance exports - always use webContainer
export const getContainerInstance = webContainer.getWebContainerInstance;

// Export types and constants
export type { CommandResult } from './webcontainer/types';
export type { NodeContainer } from './nodecontainer/types';
export { WebContainer } from '@webcontainer/api';
