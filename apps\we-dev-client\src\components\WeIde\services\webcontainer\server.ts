import { getWebContainerInstance } from './instance';
import { useFileStore } from '../../stores/fileStore';

let isServerStarting = false;
let isServerRunning = false;

// Sync editor files to WebContainer
async function syncEditorFilesToWebContainer(webcontainer: any) {
  console.log('Syncing editor files to WebContainer...');

  const { files } = useFileStore.getState();
  const fileCount = Object.keys(files).length;

  if (fileCount === 0) {
    console.log('No editor files to sync');
    return;
  }

  console.log(`Syncing ${fileCount} files from editor to WebContainer`);

  for (const [path, contents] of Object.entries(files)) {
    if (typeof contents !== 'string') continue;

    try {
      const fullPath = `/${path}`;
      // Create parent directories
      const parentDir = fullPath.substring(0, fullPath.lastIndexOf('/'));
      if (parentDir && parentDir !== '/') {
        await webcontainer.fs.mkdir(parentDir, { recursive: true });
      }
      // Write file
      await webcontainer.fs.writeFile(fullPath, contents);
      console.log(`Synced: ${path}`);
    } catch (fileError) {
      console.warn(`Failed to sync file ${path}:`, fileError);
    }
  }

  console.log('Editor files synced successfully');
}

// Create a default project structure when no files exist
async function createDefaultProject(webcontainer: any) {
  console.log('Creating default simple web project...');

  // Create a simple project with minimal dependencies
  const packageJson = {
    name: "webcontainer-app",
    private: true,
    version: "0.0.0",
    type: "module",
    scripts: {
      dev: "vite --host",
      build: "vite build",
      preview: "vite preview"
    },
    dependencies: {},
    devDependencies: {
      vite: "^5.2.0"
    }
  };

  const viteConfig = `import { defineConfig } from 'vite'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [],
  server: {
    host: true,
    port: 3000
  }
})
`;

  const indexHtml = `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebContainer App</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        min-height: 100vh;
      }
      .container {
        background: rgba(255, 255, 255, 0.1);
        padding: 2rem;
        border-radius: 10px;
        backdrop-filter: blur(10px);
      }
      button {
        background: #4CAF50;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
      }
      button:hover {
        background: #45a049;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎉 WebContainer is Working!</h1>
      <p>This is a simple web application running in WebContainer.</p>
      <button onclick="updateTime()">Click me!</button>
      <p id="time"></p>
      <script>
        function updateTime() {
          document.getElementById('time').textContent = 'Current time: ' + new Date().toLocaleTimeString();
        }
        updateTime();
      </script>
    </div>
  </body>
</html>
`;

  // Write files to WebContainer
  await webcontainer.fs.writeFile('package.json', JSON.stringify(packageJson, null, 2));
  await webcontainer.fs.writeFile('vite.config.js', viteConfig);
  await webcontainer.fs.writeFile('index.html', indexHtml);

  console.log('Default simple web project created successfully');
}

export async function startDevServer() {
  if (isServerStarting || isServerRunning) {
    console.log('Dev server is already starting or running');
    return;
  }

  const webcontainer = await getWebContainerInstance();

  if (!webcontainer) {
    throw new Error('WebContainer not available');
  }

  try {
    isServerStarting = true;

    // First, sync editor files to WebContainer
    await syncEditorFilesToWebContainer(webcontainer);

    // Check if package.json exists (after syncing editor files)
    let packageJsonExists = false;
    try {
      const packageJson = await webcontainer.fs.readFile('package.json', 'utf-8');
      packageJsonExists = true;
      console.log('Found package.json:', JSON.parse(packageJson).name || 'unnamed project');
    } catch (error) {
      console.log('No package.json found in editor files, creating default project...');
      await createDefaultProject(webcontainer);
      packageJsonExists = true;
    }

    if (!packageJsonExists) {
      throw new Error('No package.json found and failed to create default project');
    }

    console.log('Installing dependencies...');
    const installProcess = await webcontainer.spawn('npm', ['install']);

    // Capture install output for debugging
    let installOutput = '';
    installProcess.output.pipeTo(
      new WritableStream({
        write(data) {
          installOutput += data;
          console.log('npm install:', data);
        },
      }),
    );

    const installExitCode = await installProcess.exit;

    if (installExitCode !== 0) {
      console.error('npm install failed with exit code:', installExitCode);
      console.error('Install output:', installOutput);
      throw new Error(`Installation failed with exit code ${installExitCode}`);
    }

    console.log('Dependencies installed successfully');
    console.log('Starting dev server...');

    const devProcess = await webcontainer.spawn('npm', ['run', 'dev']);

    // Listen for server ready events
    webcontainer.on('server-ready', (port, url) => {
      console.log(`Development server ready at ${url} (port ${port})`);
      isServerRunning = true;
    });

    isServerStarting = false;
    return devProcess;
  } catch (error) {
    isServerStarting = false;
    console.error('Failed to start dev server:', error);
    throw error;
  }
}

export function resetServerState() {
  isServerStarting = false;
  isServerRunning = false;
}

// Export sync function for manual use
export { syncEditorFilesToWebContainer };