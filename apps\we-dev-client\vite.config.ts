import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [react()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
    },
    define: {
      "process.env.APP_BASE_URL": JSON.stringify(env.APP_BASE_URL || "http://localhost:3000"),
      "process.env.NODE_ENV": JSON.stringify(mode),
      "process.env.JWT_SECRET": JSON.stringify(env.JWT_SECRET || ""),
      // Define process as undefined to catch any remaining usage
      "process.platform": JSON.stringify("web"),
      "process.env": JSON.stringify({}),
    },
    server: {
      headers: {
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin',
      },
    },
    preview: {
      headers: {
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin',
      },
    },
  };
});
