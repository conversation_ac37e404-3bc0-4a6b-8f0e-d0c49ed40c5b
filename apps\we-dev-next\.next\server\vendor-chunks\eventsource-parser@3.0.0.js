"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventsource-parser@3.0.0";
exports.ids = ["vendor-chunks/eventsource-parser@3.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/index.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/index.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParseError: () => (/* binding */ ParseError),\n/* harmony export */   createParser: () => (/* binding */ createParser)\n/* harmony export */ });\nvar __defProp = Object.defineProperty, __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: !0, configurable: !0, writable: !0, value }) : obj[key] = value, __publicField = (obj, key, value) => __defNormalProp(obj, typeof key != \"symbol\" ? key + \"\" : key, value);\nclass ParseError extends Error {\n  constructor(message, options) {\n    super(message), __publicField(this, \"type\"), __publicField(this, \"field\"), __publicField(this, \"value\"), __publicField(this, \"line\"), this.name = \"ParseError\", this.type = options.type, this.field = options.field, this.value = options.value, this.line = options.line;\n  }\n}\nfunction noop(_arg) {\n}\nfunction createParser(callbacks) {\n  const { onEvent = noop, onError = noop, onRetry = noop, onComment } = callbacks;\n  let incompleteLine = \"\", isFirstChunk = !0, id, data = \"\", eventType = \"\";\n  function feed(newChunk) {\n    const chunk = isFirstChunk ? newChunk.replace(/^\\xEF\\xBB\\xBF/, \"\") : newChunk, [complete, incomplete] = splitLines(`${incompleteLine}${chunk}`);\n    for (const line of complete)\n      parseLine(line);\n    incompleteLine = incomplete, isFirstChunk = !1;\n  }\n  function parseLine(line) {\n    if (line === \"\") {\n      dispatchEvent();\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      onComment && onComment(line.slice(line.startsWith(\": \") ? 2 : 1));\n      return;\n    }\n    const fieldSeparatorIndex = line.indexOf(\":\");\n    if (fieldSeparatorIndex !== -1) {\n      const field = line.slice(0, fieldSeparatorIndex), offset = line[fieldSeparatorIndex + 1] === \" \" ? 2 : 1, value = line.slice(fieldSeparatorIndex + offset);\n      processField(field, value, line);\n      return;\n    }\n    processField(line, \"\", line);\n  }\n  function processField(field, value, line) {\n    switch (field) {\n      case \"event\":\n        eventType = value;\n        break;\n      case \"data\":\n        data = `${data}${value}\n`;\n        break;\n      case \"id\":\n        id = value.includes(\"\\0\") ? void 0 : value;\n        break;\n      case \"retry\":\n        /^\\d+$/.test(value) ? onRetry(parseInt(value, 10)) : onError(\n          new ParseError(`Invalid \\`retry\\` value: \"${value}\"`, {\n            type: \"invalid-retry\",\n            value,\n            line\n          })\n        );\n        break;\n      default:\n        onError(\n          new ParseError(\n            `Unknown field \"${field.length > 20 ? `${field.slice(0, 20)}\\u2026` : field}\"`,\n            { type: \"unknown-field\", field, value, line }\n          )\n        );\n        break;\n    }\n  }\n  function dispatchEvent() {\n    data.length > 0 && onEvent({\n      id,\n      event: eventType || void 0,\n      // If the data buffer's last character is a U+000A LINE FEED (LF) character,\n      // then remove the last character from the data buffer.\n      data: data.endsWith(`\n`) ? data.slice(0, -1) : data\n    }), id = void 0, data = \"\", eventType = \"\";\n  }\n  function reset(options = {}) {\n    incompleteLine && options.consume && parseLine(incompleteLine), id = void 0, data = \"\", eventType = \"\", incompleteLine = \"\";\n  }\n  return { feed, reset };\n}\nfunction splitLines(chunk) {\n  const lines = [];\n  let incompleteLine = \"\";\n  const totalLength = chunk.length;\n  for (let i = 0; i < totalLength; i++) {\n    const char = chunk[i];\n    char === \"\\r\" && chunk[i + 1] === `\n` ? (lines.push(incompleteLine), incompleteLine = \"\", i++) : char === \"\\r\" || char === `\n` ? (lines.push(incompleteLine), incompleteLine = \"\") : incompleteLine += char;\n  }\n  return [lines, incompleteLine];\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/stream.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/stream.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSourceParserStream: () => (/* binding */ EventSourceParserStream),\n/* harmony export */   ParseError: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_0__.ParseError)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(rsc)/./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/index.js\");\n\n\nclass EventSourceParserStream extends TransformStream {\n  constructor({ onError, onRetry, onComment } = {}) {\n    let parser;\n    super({\n      start(controller) {\n        parser = (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.createParser)({\n          onEvent: (event) => {\n            controller.enqueue(event);\n          },\n          onError(error) {\n            onError === \"terminate\" ? controller.error(error) : typeof onError == \"function\" && onError(error);\n          },\n          onRetry,\n          onComment\n        });\n      },\n      transform(chunk) {\n        parser.feed(chunk);\n      }\n    });\n  }\n}\n\n//# sourceMappingURL=stream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZXZlbnRzb3VyY2UtcGFyc2VyQDMuMC4wL25vZGVfbW9kdWxlcy9ldmVudHNvdXJjZS1wYXJzZXIvZGlzdC9zdHJlYW0uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBDO0FBQ0Y7QUFDeEM7QUFDQSxnQkFBZ0IsOEJBQThCLElBQUk7QUFDbEQ7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLHVEQUFZO0FBQzdCO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9ub2RlX21vZHVsZXMvLnBucG0vZXZlbnRzb3VyY2UtcGFyc2VyQDMuMC4wL25vZGVfbW9kdWxlcy9ldmVudHNvdXJjZS1wYXJzZXIvZGlzdC9zdHJlYW0uanM/NmQzYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVQYXJzZXIgfSBmcm9tIFwiLi9pbmRleC5qc1wiO1xuaW1wb3J0IHsgUGFyc2VFcnJvciB9IGZyb20gXCIuL2luZGV4LmpzXCI7XG5jbGFzcyBFdmVudFNvdXJjZVBhcnNlclN0cmVhbSBleHRlbmRzIFRyYW5zZm9ybVN0cmVhbSB7XG4gIGNvbnN0cnVjdG9yKHsgb25FcnJvciwgb25SZXRyeSwgb25Db21tZW50IH0gPSB7fSkge1xuICAgIGxldCBwYXJzZXI7XG4gICAgc3VwZXIoe1xuICAgICAgc3RhcnQoY29udHJvbGxlcikge1xuICAgICAgICBwYXJzZXIgPSBjcmVhdGVQYXJzZXIoe1xuICAgICAgICAgIG9uRXZlbnQ6IChldmVudCkgPT4ge1xuICAgICAgICAgICAgY29udHJvbGxlci5lbnF1ZXVlKGV2ZW50KTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIG9uRXJyb3IoZXJyb3IpIHtcbiAgICAgICAgICAgIG9uRXJyb3IgPT09IFwidGVybWluYXRlXCIgPyBjb250cm9sbGVyLmVycm9yKGVycm9yKSA6IHR5cGVvZiBvbkVycm9yID09IFwiZnVuY3Rpb25cIiAmJiBvbkVycm9yKGVycm9yKTtcbiAgICAgICAgICB9LFxuICAgICAgICAgIG9uUmV0cnksXG4gICAgICAgICAgb25Db21tZW50XG4gICAgICAgIH0pO1xuICAgICAgfSxcbiAgICAgIHRyYW5zZm9ybShjaHVuaykge1xuICAgICAgICBwYXJzZXIuZmVlZChjaHVuayk7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cbn1cbmV4cG9ydCB7XG4gIEV2ZW50U291cmNlUGFyc2VyU3RyZWFtLFxuICBQYXJzZUVycm9yXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RyZWFtLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/eventsource-parser@3.0.0/node_modules/eventsource-parser/dist/stream.js\n");

/***/ })

};
;