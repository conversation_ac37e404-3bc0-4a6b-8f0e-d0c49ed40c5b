// Process utilities - Web version (stubs for browser compatibility)
// These functions are disabled in the web version as they require Node.js/Electron

export function runInstallScript(scriptPath: string): Promise<void> {
    console.warn('runInstallScript is not available in web version');
    return Promise.reject(new Error('Process utilities are not available in web version'));
}

export async function getBinaryPath(name: string): Promise<string> {
    console.warn('getBinaryPath is not available in web version');
    return name; // Return the name as-is for compatibility
}

export async function isBinaryExists(name: string): Promise<boolean> {
    console.warn('isBinaryExists is not available in web version');
    return false; // Always return false in web version
}