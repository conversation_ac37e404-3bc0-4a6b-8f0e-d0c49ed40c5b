import { useEffect, useState, useRef, Dispatch, SetStateAction } from "react";
import { getContainerInstance, startDevServer } from "./WeIde/services";
import { resetWebContainer } from "./WeIde/services/webcontainer/instance";
import { Smartphone, Tablet, Laptop, Monitor, ChevronDown, Play, RotateCcw, RefreshCw, FileText } from "lucide-react";
import { findWeChatDevToolsPath } from "./EditorPreviewTabs";
import { useFileStore } from "./WeIde/stores/fileStore";
import { useTranslation } from "react-i18next";

interface PreviewIframeProps {
  setShowIframe: Dispatch<SetStateAction<string>>;
  isMinPrograme: boolean;
}
interface WindowSize {
  name: string;
  width: number | string;
  height: number | string;
  icon: React.ComponentType<{ size?: string | number }>;
}
const WINDOW_SIZES: WindowSize[] = [
  { name: "Desktop", width: '100%', height:'100%', icon: Monitor },
  { name: "Mobile", width: 375, height: 667, icon: Smartphone },
  {
    name: "Tablet",
    width: Number((768 / 1.5).toFixed(0)),
    height: Number((1024 / 1.5).toFixed(0)),
    icon: Tablet,
  },
  { name: "Laptop", width: 1366, height: 768, icon: Laptop },
];

const PreviewIframe: React.FC<PreviewIframeProps> = ({
  setShowIframe,
  isMinPrograme,
}) => {
  // IPC renderer not available in web version
  const ipcRenderer = null;
  const [url, setUrl] = useState<string>("");
  const [port, setPort] = useState<string>("");
  const { projectRoot, addFile, files } = useFileStore();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [scale, setScale] = useState<number>(1);
  const { t } = useTranslation();
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedSize, setSelectedSize] = useState<WindowSize>(WINDOW_SIZES[0]);
  const [isWindowSizeDropdownOpen, setIsWindowSizeDropdownOpen] = useState(false);
  const [iframeLoaded, setIframeLoaded] = useState(false);
  const [isStartingServer, setIsStartingServer] = useState(false);
  const [serverError, setServerError] = useState<string>("");

  useEffect(() => {
    // Set up server-ready listener only when starting the server
    // Don't create WebContainer instance automatically
    console.log('PreviewIframe mounted - waiting for manual server start');
    console.log('Cross-origin isolated:', self.crossOriginIsolated);
    console.log('SharedArrayBuffer available:', typeof SharedArrayBuffer !== 'undefined');
  }, []);

  const handleRefresh = () => {
    console.log("刷新 handleRefresh", iframeRef.current);
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

    const displayUrl = port
    ? `http://localhost:${port}`
    : isMinPrograme
      ? t('preview.wxminiPreview')
      : t('preview.noserver');

  const handleWheel = (e: WheelEvent) => {
    if (e.ctrlKey) {
      e.preventDefault();
      const delta = e.deltaY > 0 ? 0.9 : 1.1;
      setScale((prevScale) => {
        const newScale = prevScale * delta;
        return Math.min(Math.max(newScale, 0.5), 3);
      });
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let initialDistance = 0;
    let initialScale = 1;

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 2) {
        e.preventDefault();
        initialDistance = Math.hypot(
          e.touches[0].clientX - e.touches[1].clientX,
          e.touches[0].clientY - e.touches[1].clientY
        );
        initialScale = scale;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 2) {
        e.preventDefault();
        const distance = Math.hypot(
          e.touches[0].clientX - e.touches[1].clientX,
          e.touches[0].clientY - e.touches[1].clientY
        );
        const delta = distance / initialDistance;
        const newScale = Math.min(Math.max(initialScale * delta, 0.5), 3);
        setScale(newScale);
      }
    };

    container.addEventListener("wheel", handleWheel, { passive: false });
    container.addEventListener("touchstart", handleTouchStart);
    container.addEventListener("touchmove", handleTouchMove);

    return () => {
      container.removeEventListener("wheel", handleWheel);
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
    };
  }, [scale]);

  const handleZoomIn = () => {
    setScale((prevScale) => Math.min(prevScale * 1.1, 3));
  };

  const handleZoomOut = () => {
    setScale((prevScale) => Math.max(prevScale * 0.9, 0.5));
  };

  const handleZoomReset = () => {
    setScale(1);
  };

  const openExternal = () => {
    // Open in new tab for web version
    if (url) {
      window.open(url, '_blank');
    }
  };

  const handleStartServer = async () => {
    if (isStartingServer) return;

    setIsStartingServer(true);
    setServerError("");

    try {
      console.log('Starting development server...');

      // Set up server-ready listener before starting server
      const instance = await getContainerInstance();
      if (instance) {
        instance.on("server-ready", (port, url) => {
          console.log("server-ready", port, url);
          setUrl(url);
          setShowIframe("preview");
          setPort(port.toString());
          setServerError("");
        });
      }

      await startDevServer();
    } catch (error) {
      console.error('Failed to start server:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      setServerError(errorMessage);
    } finally {
      setIsStartingServer(false);
    }
  };

  const handleResetContainer = async () => {
    console.log('Resetting WebContainer...');
    await resetWebContainer();
    setUrl("");
    setPort("");
    setIsStartingServer(false);
    setServerError("");
  };

  const handleRefreshPage = () => {
    console.log('Refreshing page to clear WebContainer state...');
    window.location.reload();
  };

  const handleCreateSampleProject = async () => {
    console.log('Creating sample project in editor...');

    const sampleFiles = {
      'package.json': JSON.stringify({
        name: "my-web-app",
        private: true,
        version: "0.0.0",
        type: "module",
        scripts: {
          dev: "vite --host",
          build: "vite build",
          preview: "vite preview"
        },
        devDependencies: {
          vite: "^5.2.0"
        }
      }, null, 2),

      'index.html': `<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Web App</title>
    <style>
      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .container {
        background: rgba(255, 255, 255, 0.1);
        padding: 3rem;
        border-radius: 20px;
        backdrop-filter: blur(10px);
        text-align: center;
        max-width: 600px;
      }
      h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
      }
      p {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
      }
      button {
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 50px;
        cursor: pointer;
        font-size: 18px;
        font-weight: bold;
        transition: transform 0.2s;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      }
      button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.3);
      }
      .counter {
        font-size: 2rem;
        margin: 1rem 0;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🚀 My Web App</h1>
      <p>This is your custom web application running in WebContainer!</p>
      <div class="counter" id="counter">Count: 0</div>
      <button onclick="incrementCounter()">Click Me!</button>
      <p style="margin-top: 2rem; font-size: 1rem;">
        Edit the files in the editor and see changes here!
      </p>
      <script>
        let count = 0;
        function incrementCounter() {
          count++;
          document.getElementById('counter').textContent = 'Count: ' + count;
        }
      </script>
    </div>
  </body>
</html>`,

      'vite.config.js': `import { defineConfig } from 'vite'

export default defineConfig({
  server: {
    host: true,
    port: 3000
  }
})
`
    };

    // Add files to editor
    for (const [path, content] of Object.entries(sampleFiles)) {
      await addFile(path, content);
    }

    console.log('Sample project created in editor');
  };

  useEffect(() => {
    if (iframeLoaded && iframeRef.current?.contentWindow) {
      try {
        const injectScript = `
         
        `;
        
        const iframeWindow = iframeRef.current.contentWindow;
        const script = iframeWindow.document.createElement('script');
        script.textContent = injectScript;
        iframeWindow.document.head.appendChild(script);
        
      } catch (error) {
        console.error( error);
      }
    }
  }, [iframeLoaded]);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'REQUEST_BLOB_ACCESS') {
        const blobUrl = event.data.blobUrl;
        const requestId = event.data.requestId;
      
      
        fetch(blobUrl)
          .then(response => response.blob())
          .then(blob => {
            const reader = new FileReader();
            reader.onloadend = function() {
              const base64data = reader.result as string;
              const base64Content = base64data.split(',')[1];
              if (iframeRef.current?.contentWindow) {
                iframeRef.current.contentWindow.postMessage({
                  type: 'BLOB_ACCESS_GRANTED',
                  blobData: base64Content,
                  contentType: blob.type,
                  originalUrl: blobUrl,
                  requestId: requestId
                }, '*');
              
              }
            };
            reader.readAsDataURL(blob);
          })
          .catch(error => console.error( error));
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  return (
    <div     className="preview-container w-full h-full relative flex flex-col overflow-hidden">
      <div className="browser-header bg-white dark:bg-[#1a1a1c] border-b border-gray-200 px-4 py-1 flex items-center space-x-2">
        <div className="flex space-x-1.5">
          <div className="w-3 h-3 rounded-full bg-red-500"></div>
          <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div
            className="w-3 h-3 rounded-full bg-green-500"
            onClick={openExternal}
          ></div>
        </div>
        <div className="relative">
          <button
            className="ml-2 p-1.5 rounded hover:bg-gray-100 dark:hover:bg-[#2c2c2c] text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 flex items-center gap-2"
            onClick={() =>
              setIsWindowSizeDropdownOpen(!isWindowSizeDropdownOpen)
            }
          >
            <selectedSize.icon size={16} />
            <ChevronDown size={16} />
          </button>
          {isWindowSizeDropdownOpen && (
            <>
              <div
                className="fixed inset-0 z-50"
                onClick={() => setIsWindowSizeDropdownOpen(false)}
              />
              <div className="absolute top-8 left-0 mt-2 z-50 min-w-[240px] bg-white dark:bg-black rounded-xl shadow-2xl border border-[#E5E7EB] dark:border-[rgba(255,255,255,0.1)] overflow-hidden">
                {WINDOW_SIZES.map((size) => (
                  <button
                    key={size.name}
                    className="w-full px-4 py-3.5 text-left text-[#111827] dark:text-gray-300 text-sm whitespace-nowrap flex items-center gap-3 group hover:bg-[#F5EEFF] dark:hover:bg-gray-900 bg-white dark:bg-black"
                    onClick={async () => {
                      setSelectedSize(size);
                      setIsWindowSizeDropdownOpen(false);
                      if (isMinPrograme) {
                        // WeChat DevTools functionality not available in web version
                        console.warn('WeChat DevTools functionality is not available in web version');
                      }
                    }
}
                  >
                    <size.icon size={20} />
                    <div className="flex flex-col">
                      <span className="font-medium group-hover:text-[#6D28D9] dark:group-hover:text-[#6D28D9] transition-colors duration-200">
                        {size.name}
                      </span>
                      <span className="text-xs text-[#6B7280] dark:text-gray-400 group-hover:text-[#6D28D9] dark:group-hover:text-[#6D28D9] transition-colors duration-200">
                        {size.width} × {size.height}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </>
          )}
        </div>
        <div className="flex-1 ml-4 flex items-center">
          <div className="px-3 py-1 rounded-md text-sm text-gray-800 dark:text-gray-50 border bg-gray-50 dark:bg-[#2c2c2c] border-gray-200 dark:border-black w-full truncate">
            {displayUrl}
          </div>
          <button
            onClick={handleRefresh}
            className="ml-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-[#2c2c2c] text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <div className="ml-2 flex items-center space-x-1">
            <button
              onClick={handleZoomOut}
              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-[#2c2c2c] text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
              title="缩小"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
            <button
              onClick={handleZoomReset}
              className="px-2 py-0.5 rounded hover:bg-gray-100 dark:hover:bg-[#2c2c2c] text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 text-xs"
            >
              {Math.round(scale * 100)}%
            </button>
            <button
              onClick={handleZoomIn}
              className="p-1 rounded hover:bg-gray-100 dark:hover:bg-[#2c2c2c] text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
              title="放大"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <div
        ref={containerRef}
        className="flex-1 relative bg-white overflow-hidden rounded-b-lg flex items-center justify-center"
        style={{
          cursor: isDragging ? "grabbing" : "grab",
        }}
      >
        <div
          className="bg-white transition-all duration-200 origin-center"
          style={{
            width: String(selectedSize?.width)?.indexOf('%') > -1 ?  `${(Number.parseFloat(String(selectedSize.width)) * ((1 / scale)))}%`  : `${(Number(selectedSize.width) * (1 / scale))}px`,
            height: String(selectedSize?.height)?.indexOf('%') > -1 ?`${(Number.parseFloat(String(selectedSize.height)) * (1 / scale))}%`  : `${(Number(selectedSize.height) * (1 / scale))}px`,
            transform: `scale(${scale})`,
          }}
        >
          <iframe
            ref={iframeRef}
            src={url}
            className="w-full h-full border-none rounded-b-lg bg-white"
            style={{
              width: '100%',
              minHeight: "400px",
            }}
            title="preview"
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-downloads"
            allow="cross-origin-isolated"
            onLoad={() => setIframeLoaded(true)}
          />
        </div>
        {isMinPrograme && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-50">
            <div className="text-gray-400">{t("preview.wxminiPreview")}</div>
          </div>
        )}
        {!url && !isMinPrograme && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-50 gap-4 p-4">
            <div className="text-gray-400 text-center">{t("preview.noserver")}</div>

            {Object.keys(files).length === 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 max-w-md text-center">
                <div className="text-blue-600 text-sm font-medium mb-2">No files in editor</div>
                <div className="text-blue-700 text-sm mb-3">
                  Create some files in the editor or use the sample project below.
                </div>
                <button
                  onClick={handleCreateSampleProject}
                  className="flex items-center gap-2 px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600 mx-auto"
                >
                  <FileText size={14} />
                  Create Sample Project
                </button>
              </div>
            )}

            {serverError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 max-w-md">
                <div className="text-red-600 text-sm font-medium mb-2">Error:</div>
                <div className="text-red-700 text-sm">{serverError}</div>
                {serverError.includes("Unable to create more instances") && (
                  <div className="text-red-600 text-xs mt-2">
                    Try clicking "Reset" or "Refresh Page" to clear WebContainer state.
                  </div>
                )}
              </div>
            )}

            <div className="flex gap-2 flex-wrap justify-center">
              <button
                onClick={handleStartServer}
                disabled={isStartingServer}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Play size={16} />
                {isStartingServer ? 'Starting...' : 'Start Dev Server'}
              </button>
              <button
                onClick={handleResetContainer}
                className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
                title="Reset WebContainer (if having issues)"
              >
                <RotateCcw size={16} />
                Reset
              </button>
              {serverError.includes("Unable to create more instances") && (
                <button
                  onClick={handleRefreshPage}
                  className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600"
                  title="Refresh page to completely clear WebContainer state"
                >
                  <RefreshCw size={16} />
                  Refresh Page
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PreviewIframe;
