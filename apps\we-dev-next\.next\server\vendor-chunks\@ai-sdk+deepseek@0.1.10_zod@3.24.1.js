"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+deepseek@0.1.10_zod@3.24.1";
exports.ids = ["vendor-chunks/@ai-sdk+deepseek@0.1.10_zod@3.24.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+deepseek@0.1.10_zod@3.24.1/node_modules/@ai-sdk/deepseek/dist/index.mjs":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+deepseek@0.1.10_zod@3.24.1/node_modules/@ai-sdk/deepseek/dist/index.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDeepSeek: () => (/* binding */ createDeepSeek),\n/* harmony export */   deepseek: () => (/* binding */ deepseek)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_openai_compatible__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/openai-compatible */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+openai-compatible@0.1.10_zod@3.24.1/node_modules/@ai-sdk/openai-compatible/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @ai-sdk/provider */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.0.7/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.1.8_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs\");\n// src/deepseek-provider.ts\n\n\n\n\n// src/deepseek-metadata-extractor.ts\n\n\nvar buildDeepseekMetadata = (usage) => {\n  var _a, _b;\n  return usage == null ? void 0 : {\n    deepseek: {\n      promptCacheHitTokens: (_a = usage.prompt_cache_hit_tokens) != null ? _a : NaN,\n      promptCacheMissTokens: (_b = usage.prompt_cache_miss_tokens) != null ? _b : NaN\n    }\n  };\n};\nvar deepSeekMetadataExtractor = {\n  extractMetadata: ({ parsedBody }) => {\n    const parsed = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeValidateTypes)({\n      value: parsedBody,\n      schema: deepSeekResponseSchema\n    });\n    return !parsed.success || parsed.value.usage == null ? void 0 : buildDeepseekMetadata(parsed.value.usage);\n  },\n  createStreamExtractor: () => {\n    let usage;\n    return {\n      processChunk: (chunk) => {\n        var _a, _b;\n        const parsed = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeValidateTypes)({\n          value: chunk,\n          schema: deepSeekStreamChunkSchema\n        });\n        if (parsed.success && ((_b = (_a = parsed.value.choices) == null ? void 0 : _a[0]) == null ? void 0 : _b.finish_reason) === \"stop\" && parsed.value.usage) {\n          usage = parsed.value.usage;\n        }\n      },\n      buildMetadata: () => buildDeepseekMetadata(usage)\n    };\n  }\n};\nvar deepSeekUsageSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n  prompt_cache_hit_tokens: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().nullish(),\n  prompt_cache_miss_tokens: zod__WEBPACK_IMPORTED_MODULE_1__.z.number().nullish()\n});\nvar deepSeekResponseSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n  usage: deepSeekUsageSchema.nullish()\n});\nvar deepSeekStreamChunkSchema = zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n  choices: zod__WEBPACK_IMPORTED_MODULE_1__.z.array(\n    zod__WEBPACK_IMPORTED_MODULE_1__.z.object({\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_1__.z.string().nullish()\n    })\n  ).nullish(),\n  usage: deepSeekUsageSchema.nullish()\n});\n\n// src/deepseek-provider.ts\nfunction createDeepSeek(options = {}) {\n  var _a;\n  const baseURL = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.withoutTrailingSlash)(\n    (_a = options.baseURL) != null ? _a : \"https://api.deepseek.com/v1\"\n  );\n  const getHeaders = () => ({\n    Authorization: `Bearer ${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.loadApiKey)({\n      apiKey: options.apiKey,\n      environmentVariableName: \"DEEPSEEK_API_KEY\",\n      description: \"DeepSeek API key\"\n    })}`,\n    ...options.headers\n  });\n  const createLanguageModel = (modelId, settings = {}) => {\n    return new _ai_sdk_openai_compatible__WEBPACK_IMPORTED_MODULE_2__.OpenAICompatibleChatLanguageModel(modelId, settings, {\n      provider: `deepseek.chat`,\n      url: ({ path }) => `${baseURL}${path}`,\n      headers: getHeaders,\n      fetch: options.fetch,\n      defaultObjectGenerationMode: \"json\",\n      metadataExtractor: deepSeekMetadataExtractor\n    });\n  };\n  const provider = (modelId, settings) => createLanguageModel(modelId, settings);\n  provider.languageModel = createLanguageModel;\n  provider.chat = createLanguageModel;\n  provider.textEmbeddingModel = (modelId) => {\n    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_3__.NoSuchModelError({ modelId, modelType: \"textEmbeddingModel\" });\n  };\n  return provider;\n}\nvar deepseek = createDeepSeek();\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+deepseek@0.1.10_zod@3.24.1/node_modules/@ai-sdk/deepseek/dist/index.mjs\n");

/***/ })

};
;