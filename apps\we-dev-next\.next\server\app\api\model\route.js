"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/model/route";
exports.ids = ["app/api/model/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_Workspace_programming_javascript_we0_main_apps_we_dev_next_src_app_api_model_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/model/route.ts */ \"(rsc)/./src/app/api/model/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/model/route\",\n        pathname: \"/api/model\",\n        filename: \"route\",\n        bundlePath: \"app/api/model/route\"\n    },\n    resolvedPagePath: \"E:\\\\Workspace\\\\programming\\\\javascript\\\\we0-main\\\\apps\\\\we-dev-next\\\\src\\\\app\\\\api\\\\model\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_Workspace_programming_javascript_we0_main_apps_we_dev_next_src_app_api_model_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/model/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/model/config.ts":
/*!*************************************!*\
  !*** ./src/app/api/model/config.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelConfig: () => (/* binding */ modelConfig)\n/* harmony export */ });\n// Model configuration file\n// Configure models based on actual scenarios\nconst modelConfig = [\n    {\n        modelName: \"claude-3-5-sonnet\",\n        modelKey: \"anthropic/claude-3.5-sonnet\",\n        useImage: true,\n        provider: \"claude\",\n        description: \"Claude 3.5 Sonnet model via OpenRouter\",\n        functionCall: true\n    },\n    {\n        modelName: \"gpt-4o-mini\",\n        modelKey: \"openai/gpt-4o-mini\",\n        useImage: false,\n        provider: \"openai\",\n        description: \"GPT-4 Optimized Mini model via OpenRouter\",\n        functionCall: true\n    },\n    {\n        modelName: \"deepseek-R1\",\n        modelKey: \"deepseek/deepseek-r1\",\n        useImage: false,\n        provider: \"deepseek\",\n        description: \"Deepseek R1 model with reasoning and chain-of-thought capabilities via OpenRouter\",\n        functionCall: false\n    },\n    {\n        modelName: \"deepseek-v3\",\n        modelKey: \"deepseek/deepseek-chat\",\n        useImage: false,\n        provider: \"deepseek\",\n        description: \"Deepseek V3 model via OpenRouter\",\n        functionCall: true\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/model/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/model/route.ts":
/*!************************************!*\
  !*** ./src/app/api/model/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(rsc)/./src/app/api/model/config.ts\");\n\n\n// 获取模型配置, 可以迁移到配置中心\nasync function POST() {\n    // 过滤掉key部分\n    const config = _config__WEBPACK_IMPORTED_MODULE_1__.modelConfig.map((item)=>{\n        return {\n            label: item.modelName,\n            value: item.modelKey,\n            useImage: item.useImage,\n            description: item.description,\n            icon: item.iconUrl,\n            provider: item.provider,\n            functionCall: item.functionCall\n        };\n    });\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(config);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9tb2RlbC9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMkM7QUFDSjtBQUN2QyxvQkFBb0I7QUFDYixlQUFlRTtJQUNsQixXQUFXO0lBQ1gsTUFBTUMsU0FBU0YsZ0RBQVdBLENBQUNHLEdBQUcsQ0FBQ0MsQ0FBQUE7UUFDM0IsT0FBTztZQUNIQyxPQUFPRCxLQUFLRSxTQUFTO1lBQ3JCQyxPQUFPSCxLQUFLSSxRQUFRO1lBQ3BCQyxVQUFVTCxLQUFLSyxRQUFRO1lBQ3ZCQyxhQUFhTixLQUFLTSxXQUFXO1lBQzdCQyxNQUFNUCxLQUFLUSxPQUFPO1lBQ2xCQyxVQUFVVCxLQUFLUyxRQUFRO1lBQ3ZCQyxjQUFjVixLQUFLVSxZQUFZO1FBQ25DO0lBQ0o7SUFDQyxPQUFPZixxREFBWUEsQ0FBQ2dCLElBQUksQ0FBQ2I7QUFDOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2UtZGV2L25leHQvLi9zcmMvYXBwL2FwaS9tb2RlbC9yb3V0ZS50cz8zMTkyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuaW1wb3J0IHsgbW9kZWxDb25maWcgfSBmcm9tIFwiLi9jb25maWdcIjtcbi8vIOiOt+WPluaooeWei+mFjee9riwg5Y+v5Lul6L+B56e75Yiw6YWN572u5Lit5b+DXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVCgpIHtcbiAgICAvLyDov4fmu6TmjolrZXnpg6jliIZcbiAgICBjb25zdCBjb25maWcgPSBtb2RlbENvbmZpZy5tYXAoaXRlbSA9PiB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBsYWJlbDogaXRlbS5tb2RlbE5hbWUsXG4gICAgICAgICAgICB2YWx1ZTogaXRlbS5tb2RlbEtleSxcbiAgICAgICAgICAgIHVzZUltYWdlOiBpdGVtLnVzZUltYWdlLFxuICAgICAgICAgICAgZGVzY3JpcHRpb246IGl0ZW0uZGVzY3JpcHRpb24sXG4gICAgICAgICAgICBpY29uOiBpdGVtLmljb25VcmwsXG4gICAgICAgICAgICBwcm92aWRlcjogaXRlbS5wcm92aWRlcixcbiAgICAgICAgICAgIGZ1bmN0aW9uQ2FsbDogaXRlbS5mdW5jdGlvbkNhbGwsXG4gICAgICAgIH1cbiAgICB9KVxuICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oY29uZmlnKTtcbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJtb2RlbENvbmZpZyIsIlBPU1QiLCJjb25maWciLCJtYXAiLCJpdGVtIiwibGFiZWwiLCJtb2RlbE5hbWUiLCJ2YWx1ZSIsIm1vZGVsS2V5IiwidXNlSW1hZ2UiLCJkZXNjcmlwdGlvbiIsImljb24iLCJpY29uVXJsIiwicHJvdmlkZXIiLCJmdW5jdGlvbkNhbGwiLCJqc29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/model/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1","vendor-chunks/@opentelemetry+api@1.9.0"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_235dfc4e771c7657ddae3a921583d7d1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmodel%2Froute&page=%2Fapi%2Fmodel%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmodel%2Froute.ts&appDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next%5Csrc%5Capp&pageExtensions=mdx&pageExtensions=md&pageExtensions=jsx&pageExtensions=js&pageExtensions=tsx&pageExtensions=ts&rootDir=E%3A%5CWorkspace%5Cprogramming%5Cjavascript%5Cwe0-main%5Capps%5Cwe-dev-next&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();