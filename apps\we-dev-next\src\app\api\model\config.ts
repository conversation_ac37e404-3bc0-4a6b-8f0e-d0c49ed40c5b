// Model configuration file
// Configure models based on actual scenarios

interface ModelConfig {
    modelName: string;
    modelKey: string;
    useImage: boolean;
    description?: string;
    iconUrl?: string;
    provider?: string; // Model provider
    apiKey?: string;
    apiUrl?: string;
    functionCall: boolean;
}

export const modelConfig: ModelConfig[] = [
    {
        modelName: 'claude-3-5-sonnet',
        modelKey: 'anthropic/claude-3.5-sonnet',
        useImage: true,
        provider: 'claude',
        description: 'Claude 3.5 Sonnet model via OpenRouter',
        functionCall: true,
    },
    {
        modelName: 'gpt-4o-mini',
        modelKey: 'openai/gpt-4o-mini',
        useImage: false,
        provider: 'openai',
        description: 'GPT-4 Optimized Mini model via OpenRouter',
        functionCall: true,
    },
    {
        modelName: 'deepseek-R1',
        modelKey: 'deepseek/deepseek-r1',
        useImage: false,
        provider: 'deepseek',
        description: 'Deepseek R1 model with reasoning and chain-of-thought capabilities via OpenRouter',
        functionCall: false,
    },
    {
        modelName: 'deepseek-v3',
        modelKey: 'deepseek/deepseek-chat',
        useImage: false,
        provider: 'deepseek',
        description: 'Deepseek V3 model via OpenRouter',
        functionCall: true,
    }
]
