"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+openai@1.0.19_zod@3.24.1";
exports.ids = ["vendor-chunks/@ai-sdk+openai@1.0.19_zod@3.24.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+openai@1.0.19_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+openai@1.0.19_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createOpenAI: () => (/* binding */ createOpenAI),\n/* harmony export */   openai: () => (/* binding */ openai)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.0.7_zod@3.24.1/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.0.4/node_modules/@ai-sdk/provider/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/.pnpm/zod@3.24.1/node_modules/zod/lib/index.mjs\");\n// src/openai-provider.ts\n\n\n// src/openai-chat-language-model.ts\n\n\n\n\n// src/convert-to-openai-chat-messages.ts\n\n\nfunction convertToOpenAIChatMessages({\n  prompt,\n  useLegacyFunctionCalling = false,\n  systemMessageMode = \"system\"\n}) {\n  const messages = [];\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        switch (systemMessageMode) {\n          case \"system\": {\n            messages.push({ role: \"system\", content });\n            break;\n          }\n          case \"developer\": {\n            messages.push({ role: \"developer\", content });\n            break;\n          }\n          case \"remove\": {\n            break;\n          }\n          default: {\n            const _exhaustiveCheck = systemMessageMode;\n            throw new Error(\n              `Unsupported system message mode: ${_exhaustiveCheck}`\n            );\n          }\n        }\n        break;\n      }\n      case \"user\": {\n        if (content.length === 1 && content[0].type === \"text\") {\n          messages.push({ role: \"user\", content: content[0].text });\n          break;\n        }\n        messages.push({\n          role: \"user\",\n          content: content.map((part) => {\n            var _a, _b, _c;\n            switch (part.type) {\n              case \"text\": {\n                return { type: \"text\", text: part.text };\n              }\n              case \"image\": {\n                return {\n                  type: \"image_url\",\n                  image_url: {\n                    url: part.image instanceof URL ? part.image.toString() : `data:${(_a = part.mimeType) != null ? _a : \"image/jpeg\"};base64,${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.convertUint8ArrayToBase64)(part.image)}`,\n                    // OpenAI specific extension: image detail\n                    detail: (_c = (_b = part.providerMetadata) == null ? void 0 : _b.openai) == null ? void 0 : _c.imageDetail\n                  }\n                };\n              }\n              case \"file\": {\n                if (part.data instanceof URL) {\n                  throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                    functionality: \"'File content parts with URL data' functionality not supported.\"\n                  });\n                }\n                switch (part.mimeType) {\n                  case \"audio/wav\": {\n                    return {\n                      type: \"input_audio\",\n                      input_audio: { data: part.data, format: \"wav\" }\n                    };\n                  }\n                  case \"audio/mp3\":\n                  case \"audio/mpeg\": {\n                    return {\n                      type: \"input_audio\",\n                      input_audio: { data: part.data, format: \"mp3\" }\n                    };\n                  }\n                  default: {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                      functionality: `File content part type ${part.mimeType} in user messages`\n                    });\n                  }\n                }\n              }\n            }\n          })\n        });\n        break;\n      }\n      case \"assistant\": {\n        let text = \"\";\n        const toolCalls = [];\n        for (const part of content) {\n          switch (part.type) {\n            case \"text\": {\n              text += part.text;\n              break;\n            }\n            case \"tool-call\": {\n              toolCalls.push({\n                id: part.toolCallId,\n                type: \"function\",\n                function: {\n                  name: part.toolName,\n                  arguments: JSON.stringify(part.args)\n                }\n              });\n              break;\n            }\n            default: {\n              const _exhaustiveCheck = part;\n              throw new Error(`Unsupported part: ${_exhaustiveCheck}`);\n            }\n          }\n        }\n        if (useLegacyFunctionCalling) {\n          if (toolCalls.length > 1) {\n            throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n              functionality: \"useLegacyFunctionCalling with multiple tool calls in one message\"\n            });\n          }\n          messages.push({\n            role: \"assistant\",\n            content: text,\n            function_call: toolCalls.length > 0 ? toolCalls[0].function : void 0\n          });\n        } else {\n          messages.push({\n            role: \"assistant\",\n            content: text,\n            tool_calls: toolCalls.length > 0 ? toolCalls : void 0\n          });\n        }\n        break;\n      }\n      case \"tool\": {\n        for (const toolResponse of content) {\n          if (useLegacyFunctionCalling) {\n            messages.push({\n              role: \"function\",\n              name: toolResponse.toolName,\n              content: JSON.stringify(toolResponse.result)\n            });\n          } else {\n            messages.push({\n              role: \"tool\",\n              tool_call_id: toolResponse.toolCallId,\n              content: JSON.stringify(toolResponse.result)\n            });\n          }\n        }\n        break;\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  return messages;\n}\n\n// src/map-openai-chat-logprobs.ts\nfunction mapOpenAIChatLogProbsOutput(logprobs) {\n  var _a, _b;\n  return (_b = (_a = logprobs == null ? void 0 : logprobs.content) == null ? void 0 : _a.map(({ token, logprob, top_logprobs }) => ({\n    token,\n    logprob,\n    topLogprobs: top_logprobs ? top_logprobs.map(({ token: token2, logprob: logprob2 }) => ({\n      token: token2,\n      logprob: logprob2\n    })) : []\n  }))) != null ? _b : void 0;\n}\n\n// src/map-openai-finish-reason.ts\nfunction mapOpenAIFinishReason(finishReason) {\n  switch (finishReason) {\n    case \"stop\":\n      return \"stop\";\n    case \"length\":\n      return \"length\";\n    case \"content_filter\":\n      return \"content-filter\";\n    case \"function_call\":\n    case \"tool_calls\":\n      return \"tool-calls\";\n    default:\n      return \"unknown\";\n  }\n}\n\n// src/openai-error.ts\n\n\nvar openaiErrorDataSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  error: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    message: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n    // The additional information below is handled loosely to support\n    // OpenAI-compatible providers that have slightly different error\n    // responses:\n    type: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    param: zod__WEBPACK_IMPORTED_MODULE_2__.z.any().nullish(),\n    code: zod__WEBPACK_IMPORTED_MODULE_2__.z.union([zod__WEBPACK_IMPORTED_MODULE_2__.z.string(), zod__WEBPACK_IMPORTED_MODULE_2__.z.number()]).nullish()\n  })\n});\nvar openaiFailedResponseHandler = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonErrorResponseHandler)({\n  errorSchema: openaiErrorDataSchema,\n  errorToMessage: (data) => data.error.message\n});\n\n// src/get-response-metadata.ts\nfunction getResponseMetadata({\n  id,\n  model,\n  created\n}) {\n  return {\n    id: id != null ? id : void 0,\n    modelId: model != null ? model : void 0,\n    timestamp: created != null ? new Date(created * 1e3) : void 0\n  };\n}\n\n// src/openai-prepare-tools.ts\n\nfunction prepareTools({\n  mode,\n  useLegacyFunctionCalling = false,\n  structuredOutputs\n}) {\n  var _a;\n  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;\n  const toolWarnings = [];\n  if (tools == null) {\n    return { tools: void 0, tool_choice: void 0, toolWarnings };\n  }\n  const toolChoice = mode.toolChoice;\n  if (useLegacyFunctionCalling) {\n    const openaiFunctions = [];\n    for (const tool of tools) {\n      if (tool.type === \"provider-defined\") {\n        toolWarnings.push({ type: \"unsupported-tool\", tool });\n      } else {\n        openaiFunctions.push({\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters\n        });\n      }\n    }\n    if (toolChoice == null) {\n      return {\n        functions: openaiFunctions,\n        function_call: void 0,\n        toolWarnings\n      };\n    }\n    const type2 = toolChoice.type;\n    switch (type2) {\n      case \"auto\":\n      case \"none\":\n      case void 0:\n        return {\n          functions: openaiFunctions,\n          function_call: void 0,\n          toolWarnings\n        };\n      case \"required\":\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"useLegacyFunctionCalling and toolChoice: required\"\n        });\n      default:\n        return {\n          functions: openaiFunctions,\n          function_call: { name: toolChoice.toolName },\n          toolWarnings\n        };\n    }\n  }\n  const openaiTools = [];\n  for (const tool of tools) {\n    if (tool.type === \"provider-defined\") {\n      toolWarnings.push({ type: \"unsupported-tool\", tool });\n    } else {\n      openaiTools.push({\n        type: \"function\",\n        function: {\n          name: tool.name,\n          description: tool.description,\n          parameters: tool.parameters,\n          strict: structuredOutputs ? true : void 0\n        }\n      });\n    }\n  }\n  if (toolChoice == null) {\n    return { tools: openaiTools, tool_choice: void 0, toolWarnings };\n  }\n  const type = toolChoice.type;\n  switch (type) {\n    case \"auto\":\n    case \"none\":\n    case \"required\":\n      return { tools: openaiTools, tool_choice: type, toolWarnings };\n    case \"tool\":\n      return {\n        tools: openaiTools,\n        tool_choice: {\n          type: \"function\",\n          function: {\n            name: toolChoice.toolName\n          }\n        },\n        toolWarnings\n      };\n    default: {\n      const _exhaustiveCheck = type;\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`\n      });\n    }\n  }\n}\n\n// src/openai-chat-language-model.ts\nvar OpenAIChatLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get supportsStructuredOutputs() {\n    var _a;\n    return (_a = this.settings.structuredOutputs) != null ? _a : isReasoningModel(this.modelId);\n  }\n  get defaultObjectGenerationMode() {\n    if (isAudioModel(this.modelId)) {\n      return \"tool\";\n    }\n    return this.supportsStructuredOutputs ? \"json\" : \"tool\";\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get supportsImageUrls() {\n    return !this.settings.downloadImages;\n  }\n  getArgs({\n    mode,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences,\n    responseFormat,\n    seed,\n    providerMetadata\n  }) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if ((responseFormat == null ? void 0 : responseFormat.type) === \"json\" && responseFormat.schema != null && !this.supportsStructuredOutputs) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format schema is only supported with structuredOutputs\"\n      });\n    }\n    const useLegacyFunctionCalling = this.settings.useLegacyFunctionCalling;\n    if (useLegacyFunctionCalling && this.settings.parallelToolCalls === true) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: \"useLegacyFunctionCalling with parallelToolCalls\"\n      });\n    }\n    if (useLegacyFunctionCalling && this.supportsStructuredOutputs) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n        functionality: \"structuredOutputs with useLegacyFunctionCalling\"\n      });\n    }\n    if (getSystemMessageMode(this.modelId) === \"remove\" && prompt.some((message) => message.role === \"system\")) {\n      warnings.push({\n        type: \"other\",\n        message: \"system messages are removed for this model\"\n      });\n    }\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      logit_bias: this.settings.logitBias,\n      logprobs: this.settings.logprobs === true || typeof this.settings.logprobs === \"number\" ? true : void 0,\n      top_logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      user: this.settings.user,\n      parallel_tool_calls: this.settings.parallelToolCalls,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      response_format: (responseFormat == null ? void 0 : responseFormat.type) === \"json\" ? this.supportsStructuredOutputs && responseFormat.schema != null ? {\n        type: \"json_schema\",\n        json_schema: {\n          schema: responseFormat.schema,\n          strict: true,\n          name: (_a = responseFormat.name) != null ? _a : \"response\",\n          description: responseFormat.description\n        }\n      } : { type: \"json_object\" } : void 0,\n      stop: stopSequences,\n      seed,\n      // openai specific settings:\n      // TODO remove in next major version; we auto-map maxTokens now\n      max_completion_tokens: (_b = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _b.maxCompletionTokens,\n      store: (_c = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _c.store,\n      metadata: (_d = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _d.metadata,\n      prediction: (_e = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _e.prediction,\n      reasoning_effort: (_g = (_f = providerMetadata == null ? void 0 : providerMetadata.openai) == null ? void 0 : _f.reasoningEffort) != null ? _g : this.settings.reasoningEffort,\n      // messages:\n      messages: convertToOpenAIChatMessages({\n        prompt,\n        useLegacyFunctionCalling,\n        systemMessageMode: getSystemMessageMode(this.modelId)\n      })\n    };\n    if (isReasoningModel(this.modelId)) {\n      if (baseArgs.temperature != null) {\n        baseArgs.temperature = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"temperature\",\n          details: \"temperature is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_p != null) {\n        baseArgs.top_p = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"topP\",\n          details: \"topP is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.frequency_penalty != null) {\n        baseArgs.frequency_penalty = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"frequencyPenalty\",\n          details: \"frequencyPenalty is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.presence_penalty != null) {\n        baseArgs.presence_penalty = void 0;\n        warnings.push({\n          type: \"unsupported-setting\",\n          setting: \"presencePenalty\",\n          details: \"presencePenalty is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.logit_bias != null) {\n        baseArgs.logit_bias = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"logitBias is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.logprobs != null) {\n        baseArgs.logprobs = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"logprobs is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.top_logprobs != null) {\n        baseArgs.top_logprobs = void 0;\n        warnings.push({\n          type: \"other\",\n          message: \"topLogprobs is not supported for reasoning models\"\n        });\n      }\n      if (baseArgs.max_tokens != null) {\n        if (baseArgs.max_completion_tokens == null) {\n          baseArgs.max_completion_tokens = baseArgs.max_tokens;\n        }\n        baseArgs.max_tokens = void 0;\n      }\n    }\n    switch (type) {\n      case \"regular\": {\n        const { tools, tool_choice, functions, function_call, toolWarnings } = prepareTools({\n          mode,\n          useLegacyFunctionCalling,\n          structuredOutputs: this.supportsStructuredOutputs\n        });\n        return {\n          args: {\n            ...baseArgs,\n            tools,\n            tool_choice,\n            functions,\n            function_call\n          },\n          warnings: [...warnings, ...toolWarnings]\n        };\n      }\n      case \"object-json\": {\n        return {\n          args: {\n            ...baseArgs,\n            response_format: this.supportsStructuredOutputs && mode.schema != null ? {\n              type: \"json_schema\",\n              json_schema: {\n                schema: mode.schema,\n                strict: true,\n                name: (_h = mode.name) != null ? _h : \"response\",\n                description: mode.description\n              }\n            } : { type: \"json_object\" }\n          },\n          warnings\n        };\n      }\n      case \"object-tool\": {\n        return {\n          args: useLegacyFunctionCalling ? {\n            ...baseArgs,\n            function_call: {\n              name: mode.tool.name\n            },\n            functions: [\n              {\n                name: mode.tool.name,\n                description: mode.tool.description,\n                parameters: mode.tool.parameters\n              }\n            ]\n          } : {\n            ...baseArgs,\n            tool_choice: {\n              type: \"function\",\n              function: { name: mode.tool.name }\n            },\n            tools: [\n              {\n                type: \"function\",\n                function: {\n                  name: mode.tool.name,\n                  description: mode.tool.description,\n                  parameters: mode.tool.parameters,\n                  strict: this.supportsStructuredOutputs ? true : void 0\n                }\n              }\n            ]\n          },\n          warnings\n        };\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h;\n    const { args: body, warnings } = this.getArgs(options);\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiChatResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = body;\n    const choice = response.choices[0];\n    const completionTokenDetails = (_a = response.usage) == null ? void 0 : _a.completion_tokens_details;\n    const promptTokenDetails = (_b = response.usage) == null ? void 0 : _b.prompt_tokens_details;\n    const providerMetadata = { openai: {} };\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.reasoning_tokens) != null) {\n      providerMetadata.openai.reasoningTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.reasoning_tokens;\n    }\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.accepted_prediction_tokens) != null) {\n      providerMetadata.openai.acceptedPredictionTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.accepted_prediction_tokens;\n    }\n    if ((completionTokenDetails == null ? void 0 : completionTokenDetails.rejected_prediction_tokens) != null) {\n      providerMetadata.openai.rejectedPredictionTokens = completionTokenDetails == null ? void 0 : completionTokenDetails.rejected_prediction_tokens;\n    }\n    if ((promptTokenDetails == null ? void 0 : promptTokenDetails.cached_tokens) != null) {\n      providerMetadata.openai.cachedPromptTokens = promptTokenDetails == null ? void 0 : promptTokenDetails.cached_tokens;\n    }\n    return {\n      text: (_c = choice.message.content) != null ? _c : void 0,\n      toolCalls: this.settings.useLegacyFunctionCalling && choice.message.function_call ? [\n        {\n          toolCallType: \"function\",\n          toolCallId: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n          toolName: choice.message.function_call.name,\n          args: choice.message.function_call.arguments\n        }\n      ] : (_d = choice.message.tool_calls) == null ? void 0 : _d.map((toolCall) => {\n        var _a2;\n        return {\n          toolCallType: \"function\",\n          toolCallId: (_a2 = toolCall.id) != null ? _a2 : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n          toolName: toolCall.function.name,\n          args: toolCall.function.arguments\n        };\n      }),\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      usage: {\n        promptTokens: (_f = (_e = response.usage) == null ? void 0 : _e.prompt_tokens) != null ? _f : NaN,\n        completionTokens: (_h = (_g = response.usage) == null ? void 0 : _g.completion_tokens) != null ? _h : NaN\n      },\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      response: getResponseMetadata(response),\n      warnings,\n      logprobs: mapOpenAIChatLogProbsOutput(choice.logprobs),\n      providerMetadata\n    };\n  }\n  async doStream(options) {\n    var _a;\n    if ((_a = this.settings.simulateStreaming) != null ? _a : isStreamingSimulatedByDefault(this.modelId)) {\n      const result = await this.doGenerate(options);\n      const simulatedStream = new ReadableStream({\n        start(controller) {\n          controller.enqueue({ type: \"response-metadata\", ...result.response });\n          if (result.text) {\n            controller.enqueue({\n              type: \"text-delta\",\n              textDelta: result.text\n            });\n          }\n          if (result.toolCalls) {\n            for (const toolCall of result.toolCalls) {\n              controller.enqueue({\n                type: \"tool-call-delta\",\n                toolCallType: \"function\",\n                toolCallId: toolCall.toolCallId,\n                toolName: toolCall.toolName,\n                argsTextDelta: toolCall.args\n              });\n              controller.enqueue({\n                type: \"tool-call\",\n                ...toolCall\n              });\n            }\n          }\n          controller.enqueue({\n            type: \"finish\",\n            finishReason: result.finishReason,\n            usage: result.usage,\n            logprobs: result.logprobs,\n            providerMetadata: result.providerMetadata\n          });\n          controller.close();\n        }\n      });\n      return {\n        stream: simulatedStream,\n        rawCall: result.rawCall,\n        rawResponse: result.rawResponse,\n        warnings: result.warnings\n      };\n    }\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: true,\n      // only include stream_options when in strict compatibility mode:\n      stream_options: this.config.compatibility === \"strict\" ? { include_usage: true } : void 0\n    };\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/chat/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createEventSourceResponseHandler)(\n        openaiChatChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { messages: rawPrompt, ...rawSettings } = args;\n    const toolCalls = [];\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: void 0,\n      completionTokens: void 0\n    };\n    let logprobs;\n    let isFirstChunk = true;\n    const { useLegacyFunctionCalling } = this.settings;\n    const providerMetadata = { openai: {} };\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            var _a2, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              const {\n                prompt_tokens,\n                completion_tokens,\n                prompt_tokens_details,\n                completion_tokens_details\n              } = value.usage;\n              usage = {\n                promptTokens: prompt_tokens != null ? prompt_tokens : void 0,\n                completionTokens: completion_tokens != null ? completion_tokens : void 0\n              };\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.reasoning_tokens) != null) {\n                providerMetadata.openai.reasoningTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.reasoning_tokens;\n              }\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.accepted_prediction_tokens) != null) {\n                providerMetadata.openai.acceptedPredictionTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.accepted_prediction_tokens;\n              }\n              if ((completion_tokens_details == null ? void 0 : completion_tokens_details.rejected_prediction_tokens) != null) {\n                providerMetadata.openai.rejectedPredictionTokens = completion_tokens_details == null ? void 0 : completion_tokens_details.rejected_prediction_tokens;\n              }\n              if ((prompt_tokens_details == null ? void 0 : prompt_tokens_details.cached_tokens) != null) {\n                providerMetadata.openai.cachedPromptTokens = prompt_tokens_details == null ? void 0 : prompt_tokens_details.cached_tokens;\n              }\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.delta) == null) {\n              return;\n            }\n            const delta = choice.delta;\n            if (delta.content != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: delta.content\n              });\n            }\n            const mappedLogprobs = mapOpenAIChatLogProbsOutput(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n            const mappedToolCalls = useLegacyFunctionCalling && delta.function_call != null ? [\n              {\n                type: \"function\",\n                id: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                function: delta.function_call,\n                index: 0\n              }\n            ] : delta.tool_calls;\n            if (mappedToolCalls != null) {\n              for (const toolCallDelta of mappedToolCalls) {\n                const index = toolCallDelta.index;\n                if (toolCalls[index] == null) {\n                  if (toolCallDelta.type !== \"function\") {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function' type.`\n                    });\n                  }\n                  if (toolCallDelta.id == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'id' to be a string.`\n                    });\n                  }\n                  if (((_a2 = toolCallDelta.function) == null ? void 0 : _a2.name) == null) {\n                    throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidResponseDataError({\n                      data: toolCallDelta,\n                      message: `Expected 'function.name' to be a string.`\n                    });\n                  }\n                  toolCalls[index] = {\n                    id: toolCallDelta.id,\n                    type: \"function\",\n                    function: {\n                      name: toolCallDelta.function.name,\n                      arguments: (_b = toolCallDelta.function.arguments) != null ? _b : \"\"\n                    },\n                    hasFinished: false\n                  };\n                  const toolCall2 = toolCalls[index];\n                  if (((_c = toolCall2.function) == null ? void 0 : _c.name) != null && ((_d = toolCall2.function) == null ? void 0 : _d.arguments) != null) {\n                    if (toolCall2.function.arguments.length > 0) {\n                      controller.enqueue({\n                        type: \"tool-call-delta\",\n                        toolCallType: \"function\",\n                        toolCallId: toolCall2.id,\n                        toolName: toolCall2.function.name,\n                        argsTextDelta: toolCall2.function.arguments\n                      });\n                    }\n                    if ((0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.isParsableJson)(toolCall2.function.arguments)) {\n                      controller.enqueue({\n                        type: \"tool-call\",\n                        toolCallType: \"function\",\n                        toolCallId: (_e = toolCall2.id) != null ? _e : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                        toolName: toolCall2.function.name,\n                        args: toolCall2.function.arguments\n                      });\n                      toolCall2.hasFinished = true;\n                    }\n                  }\n                  continue;\n                }\n                const toolCall = toolCalls[index];\n                if (toolCall.hasFinished) {\n                  continue;\n                }\n                if (((_f = toolCallDelta.function) == null ? void 0 : _f.arguments) != null) {\n                  toolCall.function.arguments += (_h = (_g = toolCallDelta.function) == null ? void 0 : _g.arguments) != null ? _h : \"\";\n                }\n                controller.enqueue({\n                  type: \"tool-call-delta\",\n                  toolCallType: \"function\",\n                  toolCallId: toolCall.id,\n                  toolName: toolCall.function.name,\n                  argsTextDelta: (_i = toolCallDelta.function.arguments) != null ? _i : \"\"\n                });\n                if (((_j = toolCall.function) == null ? void 0 : _j.name) != null && ((_k = toolCall.function) == null ? void 0 : _k.arguments) != null && (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.isParsableJson)(toolCall.function.arguments)) {\n                  controller.enqueue({\n                    type: \"tool-call\",\n                    toolCallType: \"function\",\n                    toolCallId: (_l = toolCall.id) != null ? _l : (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId)(),\n                    toolName: toolCall.function.name,\n                    args: toolCall.function.arguments\n                  });\n                  toolCall.hasFinished = true;\n                }\n              }\n            }\n          },\n          flush(controller) {\n            var _a2, _b;\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage: {\n                promptTokens: (_a2 = usage.promptTokens) != null ? _a2 : NaN,\n                completionTokens: (_b = usage.completionTokens) != null ? _b : NaN\n              },\n              ...providerMetadata != null ? { providerMetadata } : {}\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      request: { body: JSON.stringify(body) },\n      warnings\n    };\n  }\n};\nvar openaiTokenUsageSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n  completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n  prompt_tokens_details: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    cached_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish()\n  }).nullish(),\n  completion_tokens_details: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    reasoning_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n    accepted_prediction_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n    rejected_prediction_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish()\n  }).nullish()\n}).nullish();\nvar openaiChatResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n      message: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        role: zod__WEBPACK_IMPORTED_MODULE_2__.z.literal(\"assistant\").nullish(),\n        content: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n        function_call: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n          arguments: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n          name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string()\n        }).nullish(),\n        tool_calls: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n          zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n            type: zod__WEBPACK_IMPORTED_MODULE_2__.z.literal(\"function\"),\n            function: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n              name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n              arguments: zod__WEBPACK_IMPORTED_MODULE_2__.z.string()\n            })\n          })\n        ).nullish()\n      }),\n      index: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n      logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        content: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n          zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n            token: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n            logprob: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n            top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n              zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n                token: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n                logprob: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n              })\n            )\n          })\n        ).nullable()\n      }).nullish(),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish()\n    })\n  ),\n  usage: openaiTokenUsageSchema\n});\nvar openaiChatChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n    model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n      zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        delta: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n          role: zod__WEBPACK_IMPORTED_MODULE_2__.z.enum([\"assistant\"]).nullish(),\n          content: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n          function_call: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n            name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n            arguments: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n          }).nullish(),\n          tool_calls: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n            zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n              index: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n              id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n              type: zod__WEBPACK_IMPORTED_MODULE_2__.z.literal(\"function\").optional(),\n              function: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n                name: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n                arguments: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish()\n              })\n            })\n          ).nullish()\n        }).nullish(),\n        logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n          content: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n            zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n              token: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n              logprob: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n              top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n                zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n                  token: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n                  logprob: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n                })\n              )\n            })\n          ).nullable()\n        }).nullish(),\n        finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullable().optional(),\n        index: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n      })\n    ),\n    usage: openaiTokenUsageSchema\n  }),\n  openaiErrorDataSchema\n]);\nfunction isReasoningModel(modelId) {\n  return modelId === \"o1\" || modelId.startsWith(\"o1-\") || modelId === \"o3\" || modelId.startsWith(\"o3-\");\n}\nfunction isAudioModel(modelId) {\n  return modelId.startsWith(\"gpt-4o-audio-preview\");\n}\nfunction getSystemMessageMode(modelId) {\n  var _a, _b;\n  if (!isReasoningModel(modelId)) {\n    return \"system\";\n  }\n  return (_b = (_a = reasoningModels[modelId]) == null ? void 0 : _a.systemMessageMode) != null ? _b : \"developer\";\n}\nfunction isStreamingSimulatedByDefault(modelId) {\n  var _a, _b;\n  if (!isReasoningModel(modelId)) {\n    return false;\n  }\n  return (_b = (_a = reasoningModels[modelId]) == null ? void 0 : _a.simulateStreamingByDefault) != null ? _b : true;\n}\nvar reasoningModels = {\n  \"o1-mini\": {\n    systemMessageMode: \"remove\",\n    simulateStreamingByDefault: false\n  },\n  \"o1-mini-2024-09-12\": {\n    systemMessageMode: \"remove\",\n    simulateStreamingByDefault: false\n  },\n  \"o1-preview\": {\n    systemMessageMode: \"remove\",\n    simulateStreamingByDefault: false\n  },\n  \"o1-preview-2024-09-12\": {\n    systemMessageMode: \"remove\",\n    simulateStreamingByDefault: false\n  }\n};\n\n// src/openai-completion-language-model.ts\n\n\n\n\n// src/convert-to-openai-completion-prompt.ts\n\nfunction convertToOpenAICompletionPrompt({\n  prompt,\n  inputFormat,\n  user = \"user\",\n  assistant = \"assistant\"\n}) {\n  if (inputFormat === \"prompt\" && prompt.length === 1 && prompt[0].role === \"user\" && prompt[0].content.length === 1 && prompt[0].content[0].type === \"text\") {\n    return { prompt: prompt[0].content[0].text };\n  }\n  let text = \"\";\n  if (prompt[0].role === \"system\") {\n    text += `${prompt[0].content}\n\n`;\n    prompt = prompt.slice(1);\n  }\n  for (const { role, content } of prompt) {\n    switch (role) {\n      case \"system\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.InvalidPromptError({\n          message: \"Unexpected system message in prompt: ${content}\",\n          prompt\n        });\n      }\n      case \"user\": {\n        const userMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"image\": {\n              throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                functionality: \"images\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${user}:\n${userMessage}\n\n`;\n        break;\n      }\n      case \"assistant\": {\n        const assistantMessage = content.map((part) => {\n          switch (part.type) {\n            case \"text\": {\n              return part.text;\n            }\n            case \"tool-call\": {\n              throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n                functionality: \"tool-call messages\"\n              });\n            }\n          }\n        }).join(\"\");\n        text += `${assistant}:\n${assistantMessage}\n\n`;\n        break;\n      }\n      case \"tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"tool messages\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = role;\n        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  text += `${assistant}:\n`;\n  return {\n    prompt: text,\n    stopSequences: [`\n${user}:`]\n  };\n}\n\n// src/map-openai-completion-logprobs.ts\nfunction mapOpenAICompletionLogProbs(logprobs) {\n  return logprobs == null ? void 0 : logprobs.tokens.map((token, index) => ({\n    token,\n    logprob: logprobs.token_logprobs[index],\n    topLogprobs: logprobs.top_logprobs ? Object.entries(logprobs.top_logprobs[index]).map(\n      ([token2, logprob]) => ({\n        token: token2,\n        logprob\n      })\n    ) : []\n  }));\n}\n\n// src/openai-completion-language-model.ts\nvar OpenAICompletionLanguageModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.defaultObjectGenerationMode = void 0;\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  getArgs({\n    mode,\n    inputFormat,\n    prompt,\n    maxTokens,\n    temperature,\n    topP,\n    topK,\n    frequencyPenalty,\n    presencePenalty,\n    stopSequences: userStopSequences,\n    responseFormat,\n    seed\n  }) {\n    var _a;\n    const type = mode.type;\n    const warnings = [];\n    if (topK != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"topK\"\n      });\n    }\n    if (responseFormat != null && responseFormat.type !== \"text\") {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"responseFormat\",\n        details: \"JSON response format is not supported.\"\n      });\n    }\n    const { prompt: completionPrompt, stopSequences } = convertToOpenAICompletionPrompt({ prompt, inputFormat });\n    const stop = [...stopSequences != null ? stopSequences : [], ...userStopSequences != null ? userStopSequences : []];\n    const baseArgs = {\n      // model id:\n      model: this.modelId,\n      // model specific settings:\n      echo: this.settings.echo,\n      logit_bias: this.settings.logitBias,\n      logprobs: typeof this.settings.logprobs === \"number\" ? this.settings.logprobs : typeof this.settings.logprobs === \"boolean\" ? this.settings.logprobs ? 0 : void 0 : void 0,\n      suffix: this.settings.suffix,\n      user: this.settings.user,\n      // standardized settings:\n      max_tokens: maxTokens,\n      temperature,\n      top_p: topP,\n      frequency_penalty: frequencyPenalty,\n      presence_penalty: presencePenalty,\n      seed,\n      // prompt:\n      prompt: completionPrompt,\n      // stop sequences:\n      stop: stop.length > 0 ? stop : void 0\n    };\n    switch (type) {\n      case \"regular\": {\n        if ((_a = mode.tools) == null ? void 0 : _a.length) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n            functionality: \"tools\"\n          });\n        }\n        if (mode.toolChoice) {\n          throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n            functionality: \"toolChoice\"\n          });\n        }\n        return { args: baseArgs, warnings };\n      }\n      case \"object-json\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"object-json mode\"\n        });\n      }\n      case \"object-tool\": {\n        throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.UnsupportedFunctionalityError({\n          functionality: \"object-tool mode\"\n        });\n      }\n      default: {\n        const _exhaustiveCheck = type;\n        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);\n      }\n    }\n  }\n  async doGenerate(options) {\n    const { args, warnings } = this.getArgs(options);\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body: args,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiCompletionResponseSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    const choice = response.choices[0];\n    return {\n      text: choice.text,\n      usage: {\n        promptTokens: response.usage.prompt_tokens,\n        completionTokens: response.usage.completion_tokens\n      },\n      finishReason: mapOpenAIFinishReason(choice.finish_reason),\n      logprobs: mapOpenAICompletionLogProbs(choice.logprobs),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      response: getResponseMetadata(response),\n      warnings,\n      request: { body: JSON.stringify(args) }\n    };\n  }\n  async doStream(options) {\n    const { args, warnings } = this.getArgs(options);\n    const body = {\n      ...args,\n      stream: true,\n      // only include stream_options when in strict compatibility mode:\n      stream_options: this.config.compatibility === \"strict\" ? { include_usage: true } : void 0\n    };\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/completions\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), options.headers),\n      body,\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createEventSourceResponseHandler)(\n        openaiCompletionChunkSchema\n      ),\n      abortSignal: options.abortSignal,\n      fetch: this.config.fetch\n    });\n    const { prompt: rawPrompt, ...rawSettings } = args;\n    let finishReason = \"unknown\";\n    let usage = {\n      promptTokens: Number.NaN,\n      completionTokens: Number.NaN\n    };\n    let logprobs;\n    let isFirstChunk = true;\n    return {\n      stream: response.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            if (!chunk.success) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: chunk.error });\n              return;\n            }\n            const value = chunk.value;\n            if (\"error\" in value) {\n              finishReason = \"error\";\n              controller.enqueue({ type: \"error\", error: value.error });\n              return;\n            }\n            if (isFirstChunk) {\n              isFirstChunk = false;\n              controller.enqueue({\n                type: \"response-metadata\",\n                ...getResponseMetadata(value)\n              });\n            }\n            if (value.usage != null) {\n              usage = {\n                promptTokens: value.usage.prompt_tokens,\n                completionTokens: value.usage.completion_tokens\n              };\n            }\n            const choice = value.choices[0];\n            if ((choice == null ? void 0 : choice.finish_reason) != null) {\n              finishReason = mapOpenAIFinishReason(choice.finish_reason);\n            }\n            if ((choice == null ? void 0 : choice.text) != null) {\n              controller.enqueue({\n                type: \"text-delta\",\n                textDelta: choice.text\n              });\n            }\n            const mappedLogprobs = mapOpenAICompletionLogProbs(\n              choice == null ? void 0 : choice.logprobs\n            );\n            if (mappedLogprobs == null ? void 0 : mappedLogprobs.length) {\n              if (logprobs === void 0) logprobs = [];\n              logprobs.push(...mappedLogprobs);\n            }\n          },\n          flush(controller) {\n            controller.enqueue({\n              type: \"finish\",\n              finishReason,\n              logprobs,\n              usage\n            });\n          }\n        })\n      ),\n      rawCall: { rawPrompt, rawSettings },\n      rawResponse: { headers: responseHeaders },\n      warnings,\n      request: { body: JSON.stringify(body) }\n    };\n  }\n};\nvar openaiCompletionResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n  model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n  choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n    zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n      text: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n      finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n      logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()),\n        token_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.number()),\n        top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.record(zod__WEBPACK_IMPORTED_MODULE_2__.z.string(), zod__WEBPACK_IMPORTED_MODULE_2__.z.number())).nullable()\n      }).nullish()\n    })\n  ),\n  usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n    completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n  })\n});\nvar openaiCompletionChunkSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.union([\n  zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    created: zod__WEBPACK_IMPORTED_MODULE_2__.z.number().nullish(),\n    model: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n    choices: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(\n      zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        text: zod__WEBPACK_IMPORTED_MODULE_2__.z.string(),\n        finish_reason: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().nullish(),\n        index: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n        logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n          tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()),\n          token_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.number()),\n          top_logprobs: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.record(zod__WEBPACK_IMPORTED_MODULE_2__.z.string(), zod__WEBPACK_IMPORTED_MODULE_2__.z.number())).nullable()\n        }).nullish()\n      })\n    ),\n    usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n      prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number(),\n      completion_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number()\n    }).nullish()\n  }),\n  openaiErrorDataSchema\n]);\n\n// src/openai-embedding-model.ts\n\n\n\nvar OpenAIEmbeddingModel = class {\n  constructor(modelId, settings, config) {\n    this.specificationVersion = \"v1\";\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  get maxEmbeddingsPerCall() {\n    var _a;\n    return (_a = this.settings.maxEmbeddingsPerCall) != null ? _a : 2048;\n  }\n  get supportsParallelCalls() {\n    var _a;\n    return (_a = this.settings.supportsParallelCalls) != null ? _a : true;\n  }\n  async doEmbed({\n    values,\n    headers,\n    abortSignal\n  }) {\n    if (values.length > this.maxEmbeddingsPerCall) {\n      throw new _ai_sdk_provider__WEBPACK_IMPORTED_MODULE_1__.TooManyEmbeddingValuesForCallError({\n        provider: this.provider,\n        modelId: this.modelId,\n        maxEmbeddingsPerCall: this.maxEmbeddingsPerCall,\n        values\n      });\n    }\n    const { responseHeaders, value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/embeddings\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        input: values,\n        encoding_format: \"float\",\n        dimensions: this.settings.dimensions,\n        user: this.settings.user\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiTextEmbeddingResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      embeddings: response.data.map((item) => item.embedding),\n      usage: response.usage ? { tokens: response.usage.prompt_tokens } : void 0,\n      rawResponse: { headers: responseHeaders }\n    };\n  }\n};\nvar openaiTextEmbeddingResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  data: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.object({ embedding: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.number()) })),\n  usage: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({ prompt_tokens: zod__WEBPACK_IMPORTED_MODULE_2__.z.number() }).nullish()\n});\n\n// src/openai-image-model.ts\n\n\n\n// src/openai-image-settings.ts\nvar modelMaxImagesPerCall = {\n  \"dall-e-3\": 1,\n  \"dall-e-2\": 10\n};\n\n// src/openai-image-model.ts\nvar OpenAIImageModel = class {\n  constructor(modelId, settings, config) {\n    this.modelId = modelId;\n    this.settings = settings;\n    this.config = config;\n    this.specificationVersion = \"v1\";\n  }\n  get maxImagesPerCall() {\n    var _a, _b;\n    return (_b = (_a = this.settings.maxImagesPerCall) != null ? _a : modelMaxImagesPerCall[this.modelId]) != null ? _b : 1;\n  }\n  get provider() {\n    return this.config.provider;\n  }\n  async doGenerate({\n    prompt,\n    n,\n    size,\n    aspectRatio,\n    seed,\n    providerOptions,\n    headers,\n    abortSignal\n  }) {\n    var _a;\n    const warnings = [];\n    if (aspectRatio != null) {\n      warnings.push({\n        type: \"unsupported-setting\",\n        setting: \"aspectRatio\",\n        details: \"This model does not support aspect ratio. Use `size` instead.\"\n      });\n    }\n    if (seed != null) {\n      warnings.push({ type: \"unsupported-setting\", setting: \"seed\" });\n    }\n    const { value: response } = await (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.postJsonToApi)({\n      url: this.config.url({\n        path: \"/images/generations\",\n        modelId: this.modelId\n      }),\n      headers: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.combineHeaders)(this.config.headers(), headers),\n      body: {\n        model: this.modelId,\n        prompt,\n        n,\n        size,\n        ...(_a = providerOptions.openai) != null ? _a : {},\n        response_format: \"b64_json\"\n      },\n      failedResponseHandler: openaiFailedResponseHandler,\n      successfulResponseHandler: (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.createJsonResponseHandler)(\n        openaiImageResponseSchema\n      ),\n      abortSignal,\n      fetch: this.config.fetch\n    });\n    return {\n      images: response.data.map((item) => item.b64_json),\n      warnings\n    };\n  }\n};\nvar openaiImageResponseSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n  data: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.object({ b64_json: zod__WEBPACK_IMPORTED_MODULE_2__.z.string() }))\n});\n\n// src/openai-provider.ts\nfunction createOpenAI(options = {}) {\n  var _a, _b, _c;\n  const baseURL = (_a = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.withoutTrailingSlash)(options.baseURL)) != null ? _a : \"https://api.openai.com/v1\";\n  const compatibility = (_b = options.compatibility) != null ? _b : \"compatible\";\n  const providerName = (_c = options.name) != null ? _c : \"openai\";\n  const getHeaders = () => ({\n    Authorization: `Bearer ${(0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.loadApiKey)({\n      apiKey: options.apiKey,\n      environmentVariableName: \"OPENAI_API_KEY\",\n      description: \"OpenAI\"\n    })}`,\n    \"OpenAI-Organization\": options.organization,\n    \"OpenAI-Project\": options.project,\n    ...options.headers\n  });\n  const createChatModel = (modelId, settings = {}) => new OpenAIChatLanguageModel(modelId, settings, {\n    provider: `${providerName}.chat`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch\n  });\n  const createCompletionModel = (modelId, settings = {}) => new OpenAICompletionLanguageModel(modelId, settings, {\n    provider: `${providerName}.completion`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    compatibility,\n    fetch: options.fetch\n  });\n  const createEmbeddingModel = (modelId, settings = {}) => new OpenAIEmbeddingModel(modelId, settings, {\n    provider: `${providerName}.embedding`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createImageModel = (modelId, settings = {}) => new OpenAIImageModel(modelId, settings, {\n    provider: `${providerName}.image`,\n    url: ({ path }) => `${baseURL}${path}`,\n    headers: getHeaders,\n    fetch: options.fetch\n  });\n  const createLanguageModel = (modelId, settings) => {\n    if (new.target) {\n      throw new Error(\n        \"The OpenAI model function cannot be called with the new keyword.\"\n      );\n    }\n    if (modelId === \"gpt-3.5-turbo-instruct\") {\n      return createCompletionModel(\n        modelId,\n        settings\n      );\n    }\n    return createChatModel(modelId, settings);\n  };\n  const provider = function(modelId, settings) {\n    return createLanguageModel(modelId, settings);\n  };\n  provider.languageModel = createLanguageModel;\n  provider.chat = createChatModel;\n  provider.completion = createCompletionModel;\n  provider.embedding = createEmbeddingModel;\n  provider.textEmbedding = createEmbeddingModel;\n  provider.textEmbeddingModel = createEmbeddingModel;\n  provider.image = createImageModel;\n  return provider;\n}\nvar openai = createOpenAI({\n  compatibility: \"strict\"\n  // strict for OpenAI API\n});\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+openai@1.0.19_zod@3.24.1/node_modules/@ai-sdk/openai/dist/index.mjs\n");

/***/ })

};
;